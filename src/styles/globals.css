@import "tailwindcss";


:root {

  /* Piximind colors */
  --primary-50: #EEF8D6;
  --primary-100: #DBF1AD;
  --primary-200: #C9EA84;
  --primary-300: #B7E35B;
  --primary-400: #94CF33;
  --primary-500: #79C300;
  --primary-600: #5C9400;
  --primary-700: #528506;
  --primary-800: #43690B;
  --primary-900: #38580F;

  --gray-50: #EAEEEB;
  --gray-100: #DCE0DC;
  --gray-200: #CAD1CB;
  --gray-300: #BCC6BE;
  --gray-400: #8D9D8F;
  --gray-500: #708573;
  --gray-600: #5B715E;
  --gray-700: #48594C;
  --gray-800: #333C35;
  --gray-900: #2D342F;

  --blue-50: #D6F3F2;
  --blue-100: #BEE9E6;
  --blue-200: #A2DDD8;
  --blue-300: #87D1CE;
  --blue-400: #33ABA5;
  --blue-500: #00968F;
  --blue-600: #05807B;
  --blue-700: #027873;
  --blue-800: #0A6563;
  --blue-900: #0D5452;
  --pink-50: #FCDFEA;
  --pink-100: #FFCFDF;
  --pink-200: #F5B1C9;
  --pink-300: #F696B7;
  --pink-400: #E94D82;
  --pink-500: #E0004D;
  --pink-600: #C20143;
  --pink-700: #AB013C;
  --pink-800: #900133;
  --pink-900: #53001E;

  /* System colors */

  --text-50: #F8F9FA;
  --text-100: #F1F3F4;
  --text-200: #DFE2E6;
  --text-300: #C9CED4;
  --text-400: #8E98A4;
  --text-500: #536375;
  --text-600: #3F5165;
  --text-700: #34475C;
  --text-800: #283C53;
  --text-900: #1D324A;

  --success-50: #EBFBEE;
  --success-100: #D3F9D8;
  --success-200: #B2F2BB;
  --success-300: #8CE99A;
  --success-400: #69DB7C;
  --success-500: #51CF66;
  --success-600: #40C057;
  --success-700: #37B24D;
  --success-800: #2F9E44;
  --success-900: #2B8A3E;

  --warning-50: #FFF9DB;
  --warning-100: #FFF3BF;
  --warning-200: #FFEC99;
  --warning-300: #FFE066;
  --warning-400: #FFD43B;
  --warning-500: #FCC419;
  --warning-600: #FAB005;
  --warning-700: #F59F00;
  --warning-800: #F08C00;
  --warning-900: #E67700;

  --red-0: #FFF5F5;
  --red-1: #FFE3E3;
  --red-2: #FFC9C9;
  --red-3: #FFA8A8;
  --red-4: #FF8787;
  --red-5: #FF6B6B;
  --red-6: #FA5252;
  --red-7: #F03E3E;
  --red-8: #E03131;
  --red-9: #C92A2A;


  /* Spacing */
  --spacing-scale-xs: 10px;
  --spacing-scale-sm: 12px;
  --spacing-scale-md: 16px;
  --spacing-scale-lg: 20px;
  --spacing-scale-xl: 24px;

  /* Typography */

  /* Body Text */
  --text-scale-xs: 12px;
  --text-scale-sm: 14px;
  --text-scale-md: 16px;
  --text-scale-lg: 18px;
  --text-scale-xl: 20px;

  /* Headings */
  --text-scale-h1: 34px;
  --text-scale-h2: 26px;
  --text-scale-h3: 22px;
  --text-scale-h4: 18px;
  --text-scale-h5: 16px;
  --text-scale-h6: 14px;
}

/* ===== DARK MODE ===== */
.dark {
  /* Piximind colors - inverted scale */
  --primary-50: #38580F;
  --primary-100: #43690B;
  --primary-200: #528506;
  --primary-300: #5C9400;
  --primary-400: #79C300;
  --primary-500: #94CF33;
  --primary-600: #B7E35B;
  --primary-700: #C9EA84;
  --primary-800: #DBF1AD;
  --primary-900: #EEF8D6;

  --gray-50: #2D342F;
  --gray-100: #333C35;
  --gray-200: #48594C;
  --gray-300: #5B715E;
  --gray-400: #708573;
  --gray-500: #8D9D8F;
  --gray-600: #BCC6BE;
  --gray-700: #CAD1CB;
  --gray-800: #DCE0DC;
  --gray-900: #EAEEEB;

  --blue-50: #0D5452;
  --blue-100: #0A6563;
  --blue-200: #027873;
  --blue-300: #05807B;
  --blue-400: #00968F;
  --blue-500: #33ABA5;
  --blue-600: #87D1CE;
  --blue-700: #A2DDD8;
  --blue-800: #BEE9E6;
  --blue-900: #D6F3F2;

  --pink-50: #53001E;
  --pink-100: #900133;
  --pink-200: #AB013C;
  --pink-300: #C20143;
  --pink-400: #E0004D;
  --pink-500: #E94D82;
  --pink-600: #F696B7;
  --pink-700: #F5B1C9;
  --pink-800: #FFCFDF;
  --pink-900: #FCDFEA;

  /* System colors - inverted scale */

  --text-50: #1D324A;
  --text-100: #283C53;
  --text-200: #34475C;
  --text-300: #3F5165;
  --text-400: #536375;
  --text-500: #8E98A4;
  --text-600: #C9CED4;
  --text-700: #DFE2E6;
  --text-800: #F1F3F4;
  --text-900: #F8F9FA;

  --success-50: #2B8A3E;
  --success-100: #2F9E44;
  --success-200: #37B24D;
  --success-300: #40C057;
  --success-400: #51CF66;
  --success-500: #69DB7C;
  --success-600: #8CE99A;
  --success-700: #B2F2BB;
  --success-800: #D3F9D8;
  --success-900: #EBFBEE;

  --warning-50: #E67700;
  --warning-100: #F08C00;
  --warning-200: #F59F00;
  --warning-300: #FAB005;
  --warning-400: #FCC419;
  --warning-500: #FFD43B;
  --warning-600: #FFE066;
  --warning-700: #FFEC99;
  --warning-800: #FFF3BF;
  --warning-900: #FFF9DB;

  --red-0: #C92A2A;
  --red-1: #E03131;
  --red-2: #F03E3E;
  --red-3: #FA5252;
  --red-4: #FF6B6B;
  --red-5: #FF8787;
  --red-6: #FFA8A8;
  --red-7: #FFC9C9;
  --red-8: #FFE3E3;
  --red-9: #FFF5F5;

}


@theme {
  /* Background colors */
  --color-primary: var(--primary-500);
  --color-primary-hover: var(--primary-600);
  --color-primary-active: var(--primary-700);
  --color-primary-focus: var(--primary-800);

  --color-secondary: var(--gray-100);
  --color-secondary-hover: var(--gray-200);
  --color-secondary-active: var(--gray-300);
  --color-secondary-focus: var(--gray-400);

  --color-warning: var(--warning-500);
  --color-warning-hover: var(--warning-600);
  --color-warning-active: var(--warning-700);
  --color-warning-focus: var(--warning-800);

  --color-danger: var(--red-5);
  --color-danger-hover: var(--red-6);
  --color-danger-active: var(--red-7);
  --color-danger-focus: var(--red-8);

  --color-info: var(--blue-500);
  --color-info-hover: var(--blue-600);
  --color-info-active: var(--blue-700);
  --color-info-focus: var(--blue-800);

  --color-surface: var(--gray-100);
  --color-base: var(--gray-50);

  --foreground: var(--gray-900);
  --background: var(--gray-100);
}

/* Base styling for the document */
body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: "Geist", ui-sans-serif, system-ui, sans-serif;
  line-height: 1.6;
}

/* Utility classes for responsive design */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Mobile-first responsive utilities */
@media (max-width: 640px) {
  .mobile-stack>*+* {
    margin-top: 0.75rem;
  }

  .mobile-full-width {
    width: 100%;
  }
}