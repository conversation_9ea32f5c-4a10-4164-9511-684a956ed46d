/* PixiMind Design System Tokens */

:root {

  /* Piximind colors */
  --primary-50: #EEF8D6;
  --primary-100: #DBF1AD;
  --primary-200: #C9EA84;
  --primary-300: #B7E35B;
  --primary-400: #94CF33;
  --primary-500: #79C300;
  --primary-600: #5C9400;
  --primary-700: #528506;
  --primary-800: #43690B;
  --primary-900: #38580F;

  --gray-50: #EAEEEB;
  --gray-100: #DCE0DC;
  --gray-200: #CAD1CB;
  --gray-300: #BCC6BE;
  --gray-400: #8D9D8F;
  --gray-500: #708573;
  --gray-600: #5B715E;
  --gray-700: #48594C;
  --gray-800: #333C35;
  --gray-900: #2D342F;

  --blue-50: #D6F3F2;
  --blue-100: #BEE9E6;
  --blue-200: #A2DDD8;
  --blue-300: #87D1CE;
  --blue-400: #33ABA5;
  --blue-500: #00968F;
  --blue-600: #05807B;
  --blue-700: #027873;
  --blue-800: #0A6563;
  --blue-900: #0D5452;
  --pink-50: #FCDFEA;
  --pink-100: #FFCFDF;
  --pink-200: #F5B1C9;
  --pink-300: #F696B7;
  --pink-400: #E94D82;
  --pink-500: #E0004D;
  --pink-600: #C20143;
  --pink-700: #AB013C;
  --pink-800: #900133;
  --pink-900: #53001E;

  /* System colors */

  --text-50: #F8F9FA;
  --text-100: #F1F3F4;
  --text-200: #DFE2E6;
  --text-300: #C9CED4;
  --text-400: #8E98A4;
  --text-500: #536375;
  --text-600: #3F5165;
  --text-700: #34475C;
  --text-800: #283C53;
  --text-900: #1D324A;

  --success-50: #EBFBEE;
  --success-100: #D3F9D8;
  --success-200: #B2F2BB;
  --success-300: #8CE99A;
  --success-400: #69DB7C;
  --success-500: #51CF66;
  --success-600: #40C057;
  --success-700: #37B24D;
  --success-800: #2F9E44;
  --success-900: #2B8A3E;

  --warning-50: #FFF9DB;
  --warning-100: #FFF3BF;
  --warning-200: #FFEC99;
  --warning-300: #FFE066;
  --warning-400: #FFD43B;
  --warning-500: #FCC419;
  --warning-600: #FAB005;
  --warning-700: #F59F00;
  --warning-800: #F08C00;
  --warning-900: #E67700;

  --red-0: #FFF5F5;
  --red-1: #FFE3E3;
  --red-2: #FFC9C9;
  --red-3: #FFA8A8;
  --red-4: #FF8787;
  --red-5: #FF6B6B;
  --red-6: #FA5252;
  --red-7: #F03E3E;
  --red-8: #E03131;
  --red-9: #C92A2A;


  /* Spacing */
  --spacing-scale-xs: 10px;
  --spacing-scale-sm: 12px;
  --spacing-scale-md: 16px;
  --spacing-scale-lg: 20px;
  --spacing-scale-xl: 24px;

  /* Typography */

  /* Body Text */
  --text-scale-xs: 12px;
  --text-scale-sm: 14px;
  --text-scale-md: 16px;
  --text-scale-lg: 18px;
  --text-scale-xl: 20px;

  /* Headings */
  --text-scale-h1: 34px;
  --text-scale-h2: 26px;
  --text-scale-h3: 22px;
  --text-scale-h4: 18px;
  --text-scale-h5: 16px;
  --text-scale-h6: 14px;

  /* ===== SEMANTIC TOKENS (LIGHT MODE) ===== */

  /* Background colors */
  --background-base: var(--gray-50);
  --background-layer-1: var(--gray-100);
  --background-layer-2: var(--gray-200);

  /* Surface colors */
  --surface-base: #FFFFFF;
  --surface-raised: #FFFFFF;
  --surface-overlay: #FFFFFF;

  /* Text colors */
  --color-text-primary: var(--text-900);
  --text-primary: var(--text-900);
  --text-secondary: var(--text-700);
  --text-tertiary: var(--text-500);
  --text-disabled: var(--text-400);
  --text-inverse: var(--text-50);

  /* Border colors */
  --border-base: var(--gray-300);
  --border-strong: var(--gray-500);
  --border-subtle: var(--gray-200);

  /* Accent colors (Primary) */
  --accent-content-default: var(--primary-600);
  --accent-content-hover: var(--primary-700);
  --accent-content-down: var(--primary-800);
  --accent-content-focus: var(--primary-600);
  --accent-background-default: var(--primary-500);
  --accent-background-hover: var(--primary-600);
  --accent-background-down: var(--primary-700);
  --accent-border-default: var(--primary-500);
  --accent-border-hover: var(--primary-600);
  --accent-border-focus: var(--primary-600);

  /* Status colors */
  --positive-content: var(--success-700);
  --positive-background: var(--success-100);
  --positive-border: var(--success-300);
  --negative-content: var(--red-7);
  --negative-background: var(--red-1);
  --negative-border: var(--red-3);
  --notice-content: var(--warning-700);
  --notice-background: var(--warning-100);
  --notice-border: var(--warning-300);
  --informative-content: var(--blue-700);
  --informative-background: var(--blue-100);
  --informative-border: var(--blue-300);

  /* Component spacing */
  --spacing-component-xs: 4px;
  --spacing-component-sm: 8px;
  --spacing-component-base: 12px;
  --spacing-component-md: 16px;
  --spacing-component-lg: 20px;
  --spacing-component-xl: 24px;
  --spacing-component-2xl: 32px;

  /* Component typography */
  --text-component-xs: 12px;
  --text-component-sm: 14px;
  --text-component-base: 16px;
  --text-component-lg: 18px;
  --text-component-xl: 20px;
  --text-component-2xl: 24px;
  --text-component-3xl: 30px;
  --text-component-4xl: 36px;

  /* Border radius */
  --radius-scale-small: 4px;
  --radius-scale-medium: 6px;
  --radius-scale-large: 8px;
  --radius-scale-xl: 12px;
  --radius-card: 8px;
  --radius-input: 6px;
  --radius-badge: 12px;
  --radius-progress: 4px;

  /* Shadows */
  --shadow-component-small: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-component-medium: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-component-large: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Component heights */
  --height-button-small: 32px;
  --height-button-medium: 40px;
  --height-button-large: 48px;
  --height-input-small: 32px;
  --height-input-medium: 40px;
  --height-input-large: 48px;

  /* Animation durations */
  --duration-fast: 150ms;
  --duration-medium: 250ms;
  --duration-slow: 350ms;

}

/* ===== DARK MODE ===== */
.dark {
  /* Piximind colors - inverted scale */
  --primary-50: #38580F;
  --primary-100: #43690B;
  --primary-200: #528506;
  --primary-300: #5C9400;
  --primary-400: #79C300;
  --primary-500: #94CF33;
  --primary-600: #B7E35B;
  --primary-700: #C9EA84;
  --primary-800: #DBF1AD;
  --primary-900: #EEF8D6;

  --gray-50: #2D342F;
  --gray-100: #333C35;
  --gray-200: #48594C;
  --gray-300: #5B715E;
  --gray-400: #708573;
  --gray-500: #8D9D8F;
  --gray-600: #BCC6BE;
  --gray-700: #CAD1CB;
  --gray-800: #DCE0DC;
  --gray-900: #EAEEEB;

  --blue-50: #0D5452;
  --blue-100: #0A6563;
  --blue-200: #027873;
  --blue-300: #05807B;
  --blue-400: #00968F;
  --blue-500: #33ABA5;
  --blue-600: #87D1CE;
  --blue-700: #A2DDD8;
  --blue-800: #BEE9E6;
  --blue-900: #D6F3F2;

  --pink-50: #53001E;
  --pink-100: #900133;
  --pink-200: #AB013C;
  --pink-300: #C20143;
  --pink-400: #E0004D;
  --pink-500: #E94D82;
  --pink-600: #F696B7;
  --pink-700: #F5B1C9;
  --pink-800: #FFCFDF;
  --pink-900: #FCDFEA;

  /* System colors - inverted scale */

  --text-50: #1D324A;
  --text-100: #283C53;
  --text-200: #34475C;
  --text-300: #3F5165;
  --text-400: #536375;
  --text-500: #8E98A4;
  --text-600: #C9CED4;
  --text-700: #DFE2E6;
  --text-800: #F1F3F4;
  --text-900: #F8F9FA;

  --success-50: #2B8A3E;
  --success-100: #2F9E44;
  --success-200: #37B24D;
  --success-300: #40C057;
  --success-400: #51CF66;
  --success-500: #69DB7C;
  --success-600: #8CE99A;
  --success-700: #B2F2BB;
  --success-800: #D3F9D8;
  --success-900: #EBFBEE;

  --warning-50: #E67700;
  --warning-100: #F08C00;
  --warning-200: #F59F00;
  --warning-300: #FAB005;
  --warning-400: #FCC419;
  --warning-500: #FFD43B;
  --warning-600: #FFE066;
  --warning-700: #FFEC99;
  --warning-800: #FFF3BF;
  --warning-900: #FFF9DB;

  --red-0: #C92A2A;
  --red-1: #E03131;
  --red-2: #F03E3E;
  --red-3: #FA5252;
  --red-4: #FF6B6B;
  --red-5: #FF8787;
  --red-6: #FFA8A8;
  --red-7: #FFC9C9;
  --red-8: #FFE3E3;
  --red-9: #FFF5F5;

  /* ===== SEMANTIC TOKENS (DARK MODE) ===== */

  /* Background colors - darker for dark mode */
  --background-base: var(--gray-900);
  --background-layer-1: var(--gray-800);
  --background-layer-2: var(--gray-700);

  /* Surface colors - dark surfaces */
  --surface-base: var(--gray-800);
  --surface-raised: var(--gray-700);
  --surface-overlay: var(--gray-600);

  /* Text colors - inverted for dark mode */
  --color-text-primary: var(--text-50);
  --text-primary: var(--text-50);
  --text-secondary: var(--text-200);
  --text-tertiary: var(--text-400);
  --text-disabled: var(--text-500);
  --text-inverse: var(--text-900);

  /* Border colors - adjusted for dark mode */
  --border-base: var(--gray-600);
  --border-strong: var(--gray-400);
  --border-subtle: var(--gray-700);

  /* Accent colors (Primary) - slightly adjusted for dark mode */
  --accent-content-default: var(--primary-400);
  --accent-content-hover: var(--primary-300);
  --accent-content-down: var(--primary-200);
  --accent-content-focus: var(--primary-400);
  --accent-background-default: var(--primary-600);
  --accent-background-hover: var(--primary-500);
  --accent-background-down: var(--primary-400);
  --accent-border-default: var(--primary-600);
  --accent-border-hover: var(--primary-500);
  --accent-border-focus: var(--primary-500);

  /* Status colors - adjusted for dark mode */
  --positive-content: var(--success-300);
  --positive-background: var(--success-900);
  --positive-border: var(--success-700);
  --negative-content: var(--red-3);
  --negative-background: var(--red-9);
  --negative-border: var(--red-7);
  --notice-content: var(--warning-300);
  --notice-background: var(--warning-900);
  --notice-border: var(--warning-700);
  --informative-content: var(--blue-300);
  --informative-background: var(--blue-900);
  --informative-border: var(--blue-700);

}

/* ===== SYSTEM PREFERENCE DARK MODE ===== */
@media (prefers-color-scheme: dark) {
  :root:not([class]) {
    /* Automatically apply dark mode based on system preference */
    /* Add any system-preference specific overrides here if needed */
  }
}