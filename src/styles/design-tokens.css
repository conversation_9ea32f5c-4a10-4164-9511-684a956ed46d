/* PixiMind Design System Tokens */

:root {

  /* Piximind colors */
  --primary-50: #EEF8D6;
  --pirmary-100: #DBF1AD;
  --primary-200: #C9EA84;
  --primary-300: #B7E35B;
  --primary-400: #94CF33;
  --primary-500: #79C300;
  --primary-600: #5C9400;
  --primary-700: #528506;
  --primary-800: #43690B;
  --primary-900: #38580F;

  --gray-50: #EAEEEB;
  --gray-100: #DCE0DC;
  --gray-200: #CAD1CB;
  --gray-300: #BCC6BE;
  --gray-400: #8D9D8F;
  --gray-500: #708573;
  --gray-600: #5B715E;
  --gray-700: #48594C;
  --gray-800: #333C35;
  --gray-900: #2D342F;

  --blue-50: #D6F3F2;
  --blue-100: #BEE9E6;
  --blue-200: #A2DDD8;
  --blue-300: #87D1CE;
  --blue-400: #33ABA5;
  --blue-500: #00968F;
  --blue-600: #05807B;
  --blue-700: #027873;
  --blue-800: #0A6563;
  --blue-900: #0D5452;
  --pink-50: #FCDFEA;
  --pink-100: #FFCFDF;
  --pink-200: #F5B1C9;
  --pink-300: #F696B7;
  --pink-400: #E94D82;
  --pink-500: #E0004D;
  --pink-600: #C20143;
  --pink-700: #AB013C;
  --pink-800: #900133;
  --pink-900: #53001E;

  /* System colors */

  --text-50: #F8F9FA;
  --text-100: #F1F3F4;
  --text-200: #DFE2E6;
  --text-300: #C9CED4;
  --text-400: #8E98A4;
  --text-500: #536375;
  --text-600: #3F5165;
  --text-700: #34475C;
  --text-800: #283C53;
  --text-900: #1D324A;

  --success-50: #EBFBEE;
  --success-100: #D3F9D8;
  --success-200: #B2F2BB;
  --success-300: #8CE99A;
  --success-400: #69DB7C;
  --success-500: #51CF66;
  --success-600: #40C057;
  --success-700: #37B24D;
  --success-800: #2F9E44;
  --success-900: #2B8A3E;

  --warning-50: #FFF9DB;
  --warning-100: #FFF3BF;
  --warning-200: #FFEC99;
  --warning-300: #FFE066;
  --warning-400: #FFD43B;
  --warning-500: #FCC419;
  --warning-600: #FAB005;
  --warning-700: #F59F00;
  --warning-800: #F08C00;
  --warning-900: #E67700;

  --red-0: #FFF5F5;
  --red-1: #FFE3E3;
  --red-2: #FFC9C9;
  --red-3: #FFA8A8;
  --red-4: #FF8787;
  --red-5: #FF6B6B;
  --red-6: #FA5252;
  --red-7: #F03E3E;
  --red-8: #E03131;
  --red-9: #C92A2A;


  /* Spacing */
  --spacing-scale-xs: 10px;
  --spacing-scale-sm: 12px;
  --spacing-scale-md: 16px;
  --spacing-scale-lg: 20px;
  --spacing-scale-xl: 24px;

  /* Typography */

  /* Body Text */
  --text-scale-xs: 12px;
  --text-scale-sm: 14px;
  --text-scale-md: 16px;
  --text-scale-lg: 18px;
  --text-scale-xl: 20px;

  /* Headings */
  --text-scale-h1: 34px;
  --text-scale-h2: 26px;
  --text-scale-h3: 22px;
  --text-scale-h4: 18px;
  --text-scale-h5: 16px;
  --text-scale-h6: 14px;

}

/* ===== DARK MODE ===== */
.dark {
  /* Piximind colors */
  --primary-50: #EEF8D6;
  --pirmary-100: #DBF1AD;
  --primary-200: #C9EA84;
  --primary-300: #B7E35B;
  --primary-400: #94CF33;
  --primary-500: #79C300;
  --primary-600: #5C9400;
  --primary-700: #528506;
  --primary-800: #43690B;
  --primary-900: #38580F;

  --gray-50: #EAEEEB;
  --gray-100: #DCE0DC;
  --gray-200: #CAD1CB;
  --gray-300: #BCC6BE;
  --gray-400: #8D9D8F;
  --gray-500: #708573;
  --gray-600: #5B715E;
  --gray-700: #48594C;
  --gray-800: #333C35;
  --gray-900: #2D342F;

  --blue-50: #D6F3F2;
  --blue-100: #BEE9E6;
  --blue-200: #A2DDD8;
  --blue-300: #87D1CE;
  --blue-400: #33ABA5;
  --blue-500: #00968F;
  --blue-600: #05807B;
  --blue-700: #027873;
  --blue-800: #0A6563;
  --blue-900: #0D5452;
  --pink-50: #FCDFEA;
  --pink-100: #FFCFDF;
  --pink-200: #F5B1C9;
  --pink-300: #F696B7;
  --pink-400: #E94D82;
  --pink-500: #E0004D;
  --pink-600: #C20143;
  --pink-700: #AB013C;
  --pink-800: #900133;
  --pink-900: #53001E;

  /* System colors */

  --text-50: #F8F9FA;
  --text-100: #F1F3F4;
  --text-200: #DFE2E6;
  --text-300: #C9CED4;
  --text-400: #8E98A4;
  --text-500: #536375;
  --text-600: #3F5165;
  --text-700: #34475C;
  --text-800: #283C53;
  --text-900: #1D324A;

  --success-50: #EBFBEE;
  --success-100: #D3F9D8;
  --success-200: #B2F2BB;
  --success-300: #8CE99A;
  --success-400: #69DB7C;
  --success-500: #51CF66;
  --success-600: #40C057;
  --success-700: #37B24D;
  --success-800: #2F9E44;
  --success-900: #2B8A3E;

  --warning-50: #FFF9DB;
  --warning-100: #FFF3BF;
  --warning-200: #FFEC99;
  --warning-300: #FFE066;
  --warning-400: #FFD43B;
  --warning-500: #FCC419;
  --warning-600: #FAB005;
  --warning-700: #F59F00;
  --warning-800: #F08C00;
  --warning-900: #E67700;

  --red-0: #FFF5F5;
  --red-1: #FFE3E3;
  --red-2: #FFC9C9;
  --red-3: #FFA8A8;
  --red-4: #FF8787;
  --red-5: #FF6B6B;
  --red-6: #FA5252;
  --red-7: #F03E3E;
  --red-8: #E03131;
  --red-9: #C92A2A;


}

/* ===== SYSTEM PREFERENCE DARK MODE ===== */
@media (prefers-color-scheme: dark) {
  :root:not([class]) {}
}