import { Httpstatus } from '../StandardApi';
import { format, parseISO, isValid } from 'date-fns';
import { fr } from 'date-fns/locale';

interface IRefreshApiResponse {
	status: number;
	data: {
		accessToken: string;
		refreshToken: string;
	};
}

class CommonFunction {
	private static instance: CommonFunction;

	public static getInstance(): CommonFunction {
		if (!CommonFunction.instance) {
			CommonFunction.instance = new CommonFunction();
		}
		return CommonFunction.instance;
	}

	static readonly createHeaders = ({
		withToken = true, // Kept for backward compatibility but no longer used
		contentType = 'application/json',
		customToken = '', // Kept for backward compatibility but no longer used
	}) => {
		const headers = new Headers();
		if (contentType === 'application/json') {
			headers.set('Content-Type', contentType);
			headers.set('Accept', contentType);
		}

		// No longer setting Authorization header - authentication is handled via HTTP-only cookies
		// The withToken and customToken parameters are kept for backward compatibility
		// Suppress unused variable warnings for backward compatibility parameters
		void withToken;
		void customToken;

		return headers;
	};
	static readonly handleRefresh = async (): Promise<IRefreshApiResponse> => {
		try {
			// Call the refresh endpoint - authentication is handled via HTTP-only cookies
			const response = await fetch('/api/auth/refresh', {
				method: 'POST',
				credentials: 'include', // Include cookies
				headers: {
					'Content-Type': 'application/json',
				},
			});

			if (response.ok) {
				return {
					status: Httpstatus.SuccessOK,
					data: {
						accessToken: '', // Not needed in cookie-based auth
						refreshToken: '', // Not needed in cookie-based auth
					},
				};
			} else {
				return {
					status: Httpstatus.Unauthorized,
					data: {
						accessToken: '',
						refreshToken: '',
					},
				};
			}
		} catch (err) {
			console.error('Error refreshing token:', err);
			return {
				status: Httpstatus.Internal,
				data: {
					accessToken: '',
					refreshToken: '',
				},
			};
		}
	};
	static readonly formatIsoDateToFrenshDate = (dateISO: string) => {
		try {
			const date = parseISO(dateISO);

			if (!isValid(date)) {
				return 'Date invalide';
			}

			// Format using date-fns with French locale
			// Format: "15/01/2024 à 14:30:45"
			return format(date, "dd/MM/yyyy 'à' HH:mm:ss", { locale: fr });
		} catch (error) {
			console.warn('Error formatting French date:', error);
			return 'Date invalide';
		}
	};
}
export { CommonFunction };
