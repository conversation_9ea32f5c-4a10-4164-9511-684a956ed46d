import { GetServerSideProps, GetServerSidePropsContext } from 'next';
import * as jose from 'jose';
import { User } from '@/providers/AuthProvider';

interface TokenPayload {
	email: string;
	sub: string; // User ID
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
	type: 'auth';
	exp: number;
	iat: number;
}

interface WithAuthOptions {
	requireAuth?: boolean;
	allowedRoles?: ('ADMIN' | 'USER')[];
	allowedStatuses?: ('ACTIVE' | 'INACTIVE' | 'REJECTED')[];
	redirectTo?: string;
}

interface AuthPageProps {
	user?: User | null;
	isAuthenticated?: boolean;
}

const decodeToken = async (token: string): Promise<User | null> => {
	try {
		// Get JWT secret
		const secret = process.env.NEXT_PUBLIC_JWT_SECRET;
		if (!secret) {
			console.error('NEXT_PUBLIC_JWT_SECRET is not defined');
			return null;
		}
		const secretKey = new TextEncoder().encode(secret);

		// Verify and decode the JWT
		const { payload } = await jose.jwtVerify(token, secretKey);

		// Validate payload structure
		if (
			typeof payload.email !== 'string' ||
			typeof payload.sub !== 'string' ||
			typeof payload.role !== 'string' ||
			typeof payload.status !== 'string' ||
			payload.type !== 'auth'
		) {
			console.error('Invalid token payload structure:', payload);
			return null;
		}

		// Type assertion for the payload (now safe after validation)
		const typedPayload = payload as unknown as TokenPayload;

		return {
			id: typedPayload.sub,
			fullName: typedPayload.email.split('@')[0], // Extract username from email as fallback
			email: typedPayload.email,
			role: typedPayload.role,
			status: typedPayload.status,
		};
	} catch (error) {
		// Handle specific JWT errors
		if (error instanceof Error) {
			if (error.name === 'JWTExpired') {
				console.log('Token has expired');
				return null;
			} else if (error.name === 'JWTInvalid') {
				console.log('Token is invalid');
				return null;
			} else {
				console.error('Failed to decode token:', error.message);
			}
		} else {
			console.error('Failed to decode token:', error);
		}
		return null;
	}
};

export function withAuth<P extends AuthPageProps = AuthPageProps>(
	getServerSidePropsFunc?: GetServerSideProps<P>,
	options: WithAuthOptions = {},
) {
	const {
		requireAuth = true,
		allowedRoles = ['ADMIN', 'USER'],
		allowedStatuses = ['ACTIVE'],
		redirectTo = '/signin',
	} = options;

	return async (context: GetServerSidePropsContext) => {
		const { req } = context;

		// Get access token from cookies
		const accessToken = req.cookies.accessToken;

		let user: User | null = null;
		let isAuthenticated = false;

		if (accessToken) {
			user = await decodeToken(accessToken);
			isAuthenticated = !!user;
		}

		// Check authentication requirements
		if (requireAuth && !isAuthenticated) {
			const currentPath = req.url || '/';
			return {
				redirect: {
					destination: `${redirectTo}?redirect=${encodeURIComponent(
						currentPath,
					)}`,
					permanent: false,
				},
			};
		}

		// Check role requirements
		if (user && allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
			return {
				redirect: {
					destination: '/403', // Forbidden
					permanent: false,
				},
			};
		}

		// Check status requirements
		if (
			user &&
			allowedStatuses.length > 0 &&
			!allowedStatuses.includes(user.status)
		) {
			// For inactive/rejected users, redirect to a status page or show appropriate message
			if (user.status === 'INACTIVE') {
				return {
					redirect: {
						destination: '/account-inactive',
						permanent: false,
					},
				};
			} else if (user.status === 'REJECTED') {
				return {
					redirect: {
						destination: '/account-rejected',
						permanent: false,
					},
				};
			}
		}

		// Call the original getServerSideProps if provided
		if (getServerSidePropsFunc) {
			const result = await getServerSidePropsFunc(context);

			// If the original function returns a redirect or notFound, return it as-is
			if ('redirect' in result || 'notFound' in result) {
				return result;
			}

			// Add auth data to props
			return {
				...result,
				props: {
					...result.props,
					user,
					isAuthenticated,
				},
			};
		}

		// Default return with auth data
		return {
			props: {
				user,
				isAuthenticated,
			},
		};
	};
}

// Convenience functions for common auth scenarios
export const withAuthRequired = <P extends AuthPageProps = AuthPageProps>(
	getServerSidePropsFunc?: GetServerSideProps<P>,
) => withAuth(getServerSidePropsFunc, { requireAuth: true });

export const withAdminAuth = <P extends AuthPageProps = AuthPageProps>(
	getServerSidePropsFunc?: GetServerSideProps<P>,
) =>
	withAuth(getServerSidePropsFunc, {
		requireAuth: true,
		allowedRoles: ['ADMIN'],
		allowedStatuses: ['ACTIVE'],
	});

export const withUserAuth = <P extends AuthPageProps = AuthPageProps>(
	getServerSidePropsFunc?: GetServerSideProps<P>,
) =>
	withAuth(getServerSidePropsFunc, {
		requireAuth: true,
		allowedRoles: ['USER', 'ADMIN'],
		allowedStatuses: ['ACTIVE'],
	});

export const withOptionalAuth = <P extends AuthPageProps = AuthPageProps>(
	getServerSidePropsFunc?: GetServerSideProps<P>,
) => withAuth(getServerSidePropsFunc, { requireAuth: false });

// Type for pages that use withAuth
export type AuthenticatedPageProps<P = Record<string, unknown>> = P &
	AuthPageProps;
