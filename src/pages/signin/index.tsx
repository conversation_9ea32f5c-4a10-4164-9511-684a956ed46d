import { AuthTemplate, SignInForm } from '@/components/templates';
import { Typography } from '@/components/atoms';
import type { NextPageWithLayout } from '../_app';
import Link from 'next/link';

const SignInPage: NextPageWithLayout = () => {
	return (
		<div className='w-full max-w-sm sm:max-w-md mx-auto px-200 sm:px-300 py-400 sm:py-600'>
			<SignInForm />

			<div className='mt-300 text-center'>
				<Typography
					variant='body-sm'
					color='secondary'>
					Don&apos;t have an account?{' '}
					<Link
						href='register'
						className='font-semibold text-accent-content-default hover:underline focus:outline-none focus:ring-2 focus:ring-accent-border-focus rounded'>
						Register
					</Link>
				</Typography>
			</div>
		</div>
	);
};

SignInPage.getLayout = (page) => {
	return <AuthTemplate>{page}</AuthTemplate>;
};

export default SignInPage;
