import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/solid';
import { clsx } from 'clsx';
import { Button, Typography } from '../atoms';

export interface SidebarHeaderProps {
	title: string;
	subtitle?: string;
	isCollapsed?: boolean;
	onToggle: () => void;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({
	title,
	subtitle,
	isCollapsed = false,
	onToggle,
}) => {
	return (
		<header
			className={clsx(
				'flex items-center border-b border-bg-primary',
				'p-2 min-h-16',
				'bg-bg-surface',
				'transition-all duration-200 ease-in-out',
				isCollapsed ? 'justify-center' : 'justify-between',
			)}>
			{!isCollapsed && (
				<div className='flex-1 min-w-0'>
					<Typography
						variant='h5'
						weight='semibold'
						color='primary'
						className='truncate'>
						{title}
					</Typography>
					{subtitle && (
						<Typography
							variant='caption'
							color='tertiary'
							className='truncate mt-50'>
							{subtitle}
						</Typography>
					)}
				</div>
			)}

			<div className='flex-shrink-0'>
				<Button
					variant='ghost'
					size='sm'
					onClick={onToggle}
					className='p-75 hover:bg-background-layer-1 focus:ring-accent-border-focus'
					aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}>
					{isCollapsed ? (
						<ChevronRightIcon className='w-4 h-4 text-text-secondary' />
					) : (
						<ChevronLeftIcon className='w-4 h-4 text-text-secondary' />
					)}
				</Button>
			</div>
		</header>
	);
};

SidebarHeader.displayName = 'SidebarHeader';
export default SidebarHeader;
