import { clsx } from 'clsx';

interface LoaderProps {
	size?: 'sm' | 'md' | 'lg';
	variant?: 'spinner' | 'dots' | 'pulse';
	className?: string;
	text?: string;
}

const sizeClasses = {
	sm: 'h-4 w-4',
	md: 'h-8 w-8',
	lg: 'h-12 w-12',
};

const SpinnerLoader = ({
	size = 'md',
	className,
}: {
	size: LoaderProps['size'];
	className?: string;
}) => (
	<div
		className={clsx(
			'animate-spin rounded-full border-2 border-border border-t-primary',
			sizeClasses[size!],
			className,
		)}
	/>
);

const DotsLoader = ({
	size = 'md',
	className,
}: {
	size: LoaderProps['size'];
	className?: string;
}) => {
	const dotSize =
		size === 'sm' ? 'h-1 w-1' : size === 'lg' ? 'h-3 w-3' : 'h-2 w-2';

	return (
		<div className={clsx('flex space-x-1', className)}>
			<div
				className={clsx('animate-bounce rounded-full primary', dotSize)}
				style={{ animationDelay: '0ms' }}
			/>
			<div
				className={clsx('animate-bounce rounded-full primary', dotSize)}
				style={{ animationDelay: '150ms' }}
			/>
			<div
				className={clsx('animate-bounce rounded-full primary', dotSize)}
				style={{ animationDelay: '300ms' }}
			/>
		</div>
	);
};

const PulseLoader = ({
	size = 'md',
	className,
}: {
	size: LoaderProps['size'];
	className?: string;
}) => (
	<div
		className={clsx(
			'animate-pulse rounded-full bg-secondary opacity-75',
			sizeClasses[size!],
			className,
		)}
	/>
);

export default function Loader({
	size = 'md',
	variant = 'spinner',
	className,
	text,
}: LoaderProps) {
	const renderLoader = () => {
		switch (variant) {
			case 'dots':
				return <DotsLoader size={size} />;
			case 'pulse':
				return <PulseLoader size={size} />;
			case 'spinner':
			default:
				return <SpinnerLoader size={size} />;
		}
	};

	return (
		<div
			className={clsx('flex flex-col items-center justify-center', className)}>
			{renderLoader()}
			{text && <p className='mt-2 text-sm text-muted animate-pulse'>{text}</p>}
		</div>
	);
}

// Convenience components for common use cases
export const FullScreenLoader = ({
	text = 'Loading...',
}: {
	text?: string;
}) => (
	<div className='flex items-center justify-center min-h-screen'>
		<Loader
			size='lg'
			text={text}
		/>
	</div>
);

export const InlineLoader = ({ text }: { text?: string }) => (
	<Loader
		size='sm'
		variant='dots'
		text={text}
	/>
);

export const ButtonLoader = () => (
	<Loader
		size='sm'
		className='mr-2'
	/>
);
