import { Textarea as HeadlessTextarea } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface TextareaProps
	extends React.ComponentProps<typeof HeadlessTextarea>,
		Omit<
			React.TextareaHTMLAttributes<HTMLTextAreaElement>,
			keyof React.ComponentProps<typeof HeadlessTextarea> | 'size'
		> {
	className?: string;
	textareaSize?: 'small' | 'medium' | 'large';
	variant?: 'default' | 'error';
}

const textareaSizes = {
	small: 'px-2 py-1 text-sm min-h-[5rem]',
	medium: 'px-3 py-2 text-base min-h-[6rem]',
	large: 'px-4 py-3 text-lg min-h-[8rem]',
};

const textareaVariants = {
	default: [
		'background text-foreground',
		'border border-secondary',
		'focus:border-secondary focus:ring-1 focus:ring-primary',
	],
	error: [
		'background text-foreground',
		'border border-danger-primary',
		'focus:border-danger-primary focus:ring-1 focus:ring-danger-primary',
	],
};

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
	(
		{ className, textareaSize = 'medium', variant = 'default', ...props },
		ref,
	) => {
		return (
			<HeadlessTextarea
				ref={ref}
				className={clsx(
					// Base styles
					'block w-full rounded-md resize-vertical',
					'transition-all duration-200 ease-in-out',
					'focus:outline-none',
					// Placeholder styles
					'placeholder:text-text-tertiary',
					// Disabled styles
					'disabled:cursor-not-allowed disabled:opacity-50',
					'disabled:bg-secondary',
					// Size styles
					textareaSizes[textareaSize],
					// Variant styles
					textareaVariants[variant],
					className,
				)}
				{...props}
			/>
		);
	},
);

Textarea.displayName = 'Textarea';
export default Textarea;
