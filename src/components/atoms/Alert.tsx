import { clsx } from 'clsx';

interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
	className?: string;
	children: React.ReactNode;
	variant?: 'default' | 'informative' | 'positive' | 'notice' | 'negative';
	size?: 'small' | 'medium' | 'large';
	icon?: React.ReactNode;
}

const alertVariants = {
	default: ['bg-bg-surface text-text-primary', 'border border-border-primary'],
	informative: ['bg-blue-50 text-blue-700', 'border border-blue-200'],
	positive: [
		'bg-success-primary/10 text-success-primary',
		'border border-success-primary/20',
	],
	notice: [
		'bg-warning-primary/10 text-warning-primary',
		'border border-warning-primary/20',
	],
	negative: [
		'bg-danger-primary/10 text-danger-primary',
		'border border-danger-primary/20',
	],
};

const alertSizes = {
	small: 'p-3 text-sm',
	medium: 'p-4 text-base',
	large: 'p-6 text-lg',
};

const Alert: React.FC<AlertProps> = ({
	className,
	children,
	variant = 'default',
	size = 'medium',
	icon,
	...props
}) => {
	return (
		<div
			className={clsx(
				// Base styles
				'rounded shadow-sm',
				'transition-all duration-200 ease-in-out',
				// Variant styles
				alertVariants[variant],
				// Size styles
				alertSizes[size],
				className,
			)}
			role='alert'
			{...props}>
			<div className='flex items-start gap-1.5'>
				{icon && <span className='flex-shrink-0 w-4 h-4 mt-25'>{icon}</span>}
				<div className='flex-1'>{children}</div>
			</div>
		</div>
	);
};

Alert.displayName = 'Alert';
export default Alert;
