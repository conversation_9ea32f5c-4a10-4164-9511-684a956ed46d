import { Input as HeadlessInput } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface InputProps
	extends React.ComponentProps<typeof HeadlessInput>,
		Omit<
			React.InputHTMLAttributes<HTMLInputElement>,
			keyof React.ComponentProps<typeof HeadlessInput> | 'size'
		> {
	className?: string;
	inputSize?: 'small' | 'medium' | 'large';
	variant?: 'default' | 'error';
}

const inputSizes = {
	small: 'px-1.5 py-0.5 text-responsive-sm h-input-small',
	medium: 'px-2 py-1 text-responsive-base h-input-medium',
	large: 'px-3 py-1.5 text-responsive-lg h-input-large',
};

const inputVariants = {
	default: [
		'bg-bg-surface text-text-primary',
		'border border-bg-primary',
		'focus:border-bg-primary-primary focus:ring-1 focus:ring-accent-primary',
	],
	error: [
		'bg-bg-surface text-text-primary',
		'border border-danger-primary',
		'focus:border-danger-primary focus:ring-1 focus:ring-danger-primary',
	],
};

const Input = forwardRef<HTMLInputElement, InputProps>(
	({ className, inputSize = 'medium', variant = 'default', ...props }, ref) => {
		return (
			<HeadlessInput
				ref={ref}
				className={clsx(
					// Base styles
					'block w-full rounded-md',
					'transition-all duration-200 ease-in-out',
					'focus:outline-none',
					// Placeholder styles
					'placeholder:text-text-tertiary',
					// Disabled styles
					'disabled:cursor-not-allowed disabled:opacity-50',
					'disabled:bg-bg-secondary',
					// Size styles
					inputSizes[inputSize],
					// Variant styles
					inputVariants[variant],
					className,
				)}
				{...props}
			/>
		);
	},
);

Input.displayName = 'Input';
export default Input;
