import { Button as HeadlessButton } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface ButtonProps
	extends React.ComponentProps<typeof HeadlessButton>,
		Omit<
			React.ButtonHTMLAttributes<HTMLButtonElement>,
			keyof React.ComponentProps<typeof HeadlessButton>
		> {
	className?: string;
	children: React.ReactNode;
	variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
	size?: 'sm' | 'md' | 'lg';
	icon?: React.ReactNode;
}

const buttonVariants = {
	primary: [
		'bg-accent-primary text-white',
		'hover:bg-accent-secondary',
		'active:bg-accent-secondary',
		'focus:ring-accent-primary',
	],
	secondary: [
		'bg-bg-surface text-text-primary border border-border-primary',
		'hover:bg-bg-secondary hover:border-border-secondary',
		'active:bg-bg-surface',
		'focus:ring-accent-primary',
	],
	outline: [
		'bg-transparent text-accent-primary border border-accent-primary',
		'hover:bg-accent-primary/10 hover:border-accent-secondary',
		'active:bg-accent-primary/20',
		'focus:ring-accent-primary',
	],
	ghost: [
		'bg-transparent text-text-secondary',
		'hover:bg-bg-surface hover:text-text-primary',
		'active:bg-bg-secondary',
		'focus:ring-accent-primary',
	],
	destructive: [
		'bg-danger-primary text-white',
		'hover:bg-danger-secondary',
		'active:bg-danger-secondary',
		'focus:ring-danger-primary',
	],
};

const buttonSizes = {
	sm: 'px-3 py-1.5 text-sm h-8',
	md: 'px-4 py-2 text-base h-10',
	lg: 'px-6 py-3 text-lg h-12',
};

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
	(
		{ className, children, variant = 'primary', size = 'md', icon, ...props },
		ref,
	) => {
		return (
			<HeadlessButton
				ref={ref}
				className={clsx(
					// Base styles
					'inline-flex items-center justify-center font-medium',
					'rounded-md transition-all duration-200 ease-in-out',
					'focus:outline-none focus:ring-2 focus:ring-offset-2',
					'focus:ring-offset-bg-base',
					'disabled:opacity-50 disabled:cursor-not-allowed',
					// Variant styles
					buttonVariants[variant],
					// Size styles
					buttonSizes[size],
					className,
				)}
				{...props}>
				{icon && <span className='mr-1'>{icon}</span>}
				{children}
			</HeadlessButton>
		);
	},
);

Button.displayName = 'Button';

export default Button;
