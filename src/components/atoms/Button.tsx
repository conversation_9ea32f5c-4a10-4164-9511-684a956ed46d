import { Button as HeadlessButton } from '@headlessui/react';
import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface ButtonProps
	extends React.ComponentProps<typeof HeadlessButton>,
		Omit<
			React.ButtonHTMLAttributes<HTMLButtonElement>,
			keyof React.ComponentProps<typeof HeadlessButton>
		> {
	className?: string;
	children: React.ReactNode;
	variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
	size?: 'sm' | 'md' | 'lg';
	icon?: React.ReactNode;
}

const buttonVariants = {
	primary: [
		'bg-primary text-white',
		'hover:bg-primary-hover',
		'active:bg-primary-active',
		'focus:ring-primary',
	],
	secondary: [
		'bg-surface text-foreground border border-border',
		'hover:bg-secondary hover:border-border-secondary',
		'active:bg-surface',
		'focus:ring-primary',
	],
	outline: [
		'bg-transparent text-primary border border-border',
		'hover:bg-primary/10 hover:border-border-secondary',
		'active:bg-primary/20',
		'focus:ring-primary',
	],
	ghost: [
		'bg-transparent text-muted',
		'hover:bg-surface hover:text-foreground',
		'active:bg-secondary',
		'focus:ring-primary',
	],
	destructive: [
		'bg-danger text-white',
		'hover:bg-danger-hover',
		'active:bg-danger-active',
		'focus:ring-danger',
	],
};

const buttonSizes = {
	sm: 'px-3 py-1.5 text-sm h-8',
	md: 'px-4 py-2 text-base h-10',
	lg: 'px-6 py-3 text-lg h-12',
};

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
	(
		{ className, children, variant = 'primary', size = 'md', icon, ...props },
		ref,
	) => {
		return (
			<HeadlessButton
				ref={ref}
				className={clsx(
					// Base styles
					'inline-flex items-center justify-center',
					'rounded-md transition-all duration-200 ease-in-out',
					'focus:outline-none focus:ring-2 focus:ring-offset-2',
					'focus:ring-offset-base',
					'disabled:opacity-50 disabled:cursor-not-allowed',
					// Variant styles
					buttonVariants[variant],
					// Size styles
					buttonSizes[size],
					className,
				)}
				{...props}>
				{icon && <span className='mr-1'>{icon}</span>}
				{children}
			</HeadlessButton>
		);
	},
);

Button.displayName = 'Button';

export default Button;
