import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
	className?: string;
	children: React.ReactNode;
	variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
	size?: 'small' | 'medium' | 'large';
	emphasis?: 'light' | 'filled' | 'outline';
	icon?: React.ReactNode;
	removable?: boolean;
	onRemove?: () => void;
}

const badgeVariants = {
	primary: {
		light: ['foreground', 'bg-primary/10'],
		filled: ['text-white', 'bg-primary'],
		outline: ['foreground', 'bg-transparent', 'border-secondary'],
	},
	secondary: {
		light: ['text-foreground', 'bg-secondary'],
		filled: ['text-white', 'bg-foreground'],
		outline: ['text-foreground', 'bg-transparent', 'border-secondary'],
	},
	success: {
		light: ['text-success', 'bg-success/25'],
		filled: ['text-white', 'bg-success'],
		outline: ['text-success', 'bg-transparent', 'border-success'],
	},
	warning: {
		light: ['text-warning', 'bg-warning/25'],
		filled: ['text-white', 'bg-warning'],
		outline: ['text-warning', 'bg-transparent', 'border-warning'],
	},
	error: {
		light: ['text-danger-primary', 'bg-danger-primary/25'],
		filled: ['text-white', 'bg-danger-primary'],
		outline: ['text-danger-primary', 'bg-transparent', 'border-danger-primary'],
	},
	info: {
		light: ['text-blue-500', 'bg-blue-500/25'],
		filled: ['text-white', 'bg-blue-600'],
		outline: ['text-blue-600', 'bg-transparent', 'border-blue-600'],
	},
};

const badgeSizes = {
	small: {
		base: 'px-2 p-0.5 text-xs',
		icon: 'w-3 h-3',
		remove: 'w-3 h-3 ml-0.5',
	},
	medium: {
		base: 'px-1.5 py-0.5 text-sm',
		icon: 'w-3.5 h-3.5',
		remove: 'w-3.5 h-3.5 ml-1',
	},
	large: {
		base: 'px-2 py-1 text-base',
		icon: 'w-4 h-4',
		remove: 'w-4 h-4 ml-1.5',
	},
};

const Badge = forwardRef<HTMLSpanElement, BadgeProps>(
	(
		{
			className,
			children,
			variant = 'primary',
			size = 'medium',
			emphasis = 'light',
			icon,
			removable = false,
			onRemove,
			...props
		},
		ref,
	) => {
		const handleRemove = (e: React.MouseEvent) => {
			e.stopPropagation();
			onRemove?.();
		};

		return (
			<span
				ref={ref}
				className={clsx(
					// Base styles
					'inline-flex items-center justify-center',
					'font-medium uppercase rounded-md',
					'transition-all duration-200 ease-in-out',
					'whitespace-nowrap',
					// Size styles
					badgeSizes[size].base,
					// Variant and emphasis styles
					badgeVariants[variant][emphasis],
					className,
				)}
				{...props}>
				{icon && (
					<span className={clsx('flex-shrink-0', badgeSizes[size].icon)}>
						{icon}
					</span>
				)}

				<span className={clsx(icon && 'ml-1')}>{children}</span>

				{removable && (
					<button
						type='button'
						onClick={handleRemove}
						className={clsx(
							'flex-shrink-0 rounded',
							'hover:bg-black/10 dark:hover:bg-white/10',
							'focus:outline-none focus:ring-1 focus:ring-current',
							'transition-colors duration-150',
							badgeSizes[size].remove,
						)}
						aria-label='Remove badge'>
						<svg
							className='w-full h-full'
							fill='none'
							stroke='currentColor'
							viewBox='0 0 24 24'>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M6 18L18 6M6 6l12 12'
							/>
						</svg>
					</button>
				)}
			</span>
		);
	},
);

Badge.displayName = 'Badge';

export default Badge;
export type { BadgeProps };
