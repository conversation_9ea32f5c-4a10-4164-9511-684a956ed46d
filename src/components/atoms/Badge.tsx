import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
	className?: string;
	children: React.ReactNode;
	variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
	size?: 'small' | 'medium' | 'large';
	emphasis?: 'light' | 'strong' | 'outline';
	icon?: React.ReactNode;
	removable?: boolean;
	onRemove?: () => void;
}

const badgeVariants = {
	primary: {
		subtle: ['text-text-primary', 'bg-bg-secondary', 'border-border-primary'],
		strong: ['text-text-primary', 'bg-bg-primary', 'border-border-primary'],
		outline: ['text-text-primary', 'bg-transparent', 'border-border-secondary'],
	},
	secondary: {
		subtle: [
			'text-text-primary',
			'bg-accent-primary/10',
			'border-accent-primary/20',
		],
		strong: ['text-white', 'bg-accent-primary', 'border-accent-primary'],
		outline: ['text-accent-primary', 'bg-transparent', 'border-accent-primary'],
	},
	success: {
		subtle: [
			'text-success-primary',
			'bg-success-primary/10',
			'border-success-primary/30',
		],
		strong: ['text-white', 'bg-success-primary', 'border-success-primary'],
		outline: [
			'text-success-primary',
			'bg-transparent',
			'border-success-primary',
		],
	},
	warning: {
		subtle: [
			'text-danger-primary',
			'bg-danger-primary/10',
			'border-danger-primary/30',
		],
		strong: ['text-white', 'bg-danger-primary', 'border-danger-primary'],
		outline: ['text-danger-primary', 'bg-transparent', 'border-danger-primary'],
	},
	error: {
		subtle: [
			'text-warning-primary',
			'bg-warning-primary/10',
			'border-warning-primary/30',
		],
		strong: ['text-white', 'bg-warning-primary', 'border-warning-primary'],
		outline: [
			'text-warning-primary',
			'bg-transparent',
			'border-warning-primary',
		],
	},
	info: {
		subtle: ['text-blue-700', 'bg-blue-50', 'border-blue-200'],
		strong: ['text-white', 'bg-blue-600', 'border-blue-600'],
		outline: ['text-blue-600', 'bg-transparent', 'border-blue-600'],
	},
};

const badgeSizes = {
	small: {
		base: 'px-1 p-0.5 text-xs',
		icon: 'w-3 h-3',
		remove: 'w-3 h-3 ml-0.5',
	},
	medium: {
		base: 'px-1.5 py-0.5 text-sm',
		icon: 'w-3.5 h-3.5',
		remove: 'w-3.5 h-3.5 ml-1',
	},
	large: {
		base: 'px-2 py-1 text-base',
		icon: 'w-4 h-4',
		remove: 'w-4 h-4 ml-1.5',
	},
};

const Badge = forwardRef<HTMLSpanElement, BadgeProps>(
	(
		{
			className,
			children,
			variant = 'primary',
			size = 'medium',
			emphasis = 'subtle',
			icon,
			removable = false,
			onRemove,
			...props
		},
		ref,
	) => {
		const handleRemove = (e: React.MouseEvent) => {
			e.stopPropagation();
			onRemove?.();
		};

		return (
			<span
				ref={ref}
				className={clsx(
					// Base styles
					'inline-flex text-xs items-center justify-center',
					'font-medium rounded-md border',
					'transition-all duration-200 ease-in-out',
					'whitespace-nowrap',
					// Size styles
					badgeSizes[size].base,
					// Variant and emphasis styles
					badgeVariants[variant][emphasis],
					className,
				)}
				{...props}>
				{icon && (
					<span className={clsx('flex-shrink-0', badgeSizes[size].icon)}>
						{icon}
					</span>
				)}

				<span className={clsx(icon && 'ml-1')}>{children}</span>

				{removable && (
					<button
						type='button'
						onClick={handleRemove}
						className={clsx(
							'flex-shrink-0 rounded',
							'hover:bg-black/10 dark:hover:bg-white/10',
							'focus:outline-none focus:ring-1 focus:ring-current',
							'transition-colors duration-150',
							badgeSizes[size].remove,
						)}
						aria-label='Remove badge'>
						<svg
							className='w-full h-full'
							fill='none'
							stroke='currentColor'
							viewBox='0 0 24 24'>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M6 18L18 6M6 6l12 12'
							/>
						</svg>
					</button>
				)}
			</span>
		);
	},
);

Badge.displayName = 'Badge';

export default Badge;
export type { BadgeProps };
