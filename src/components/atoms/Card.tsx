import { forwardRef } from 'react';
import { clsx } from 'clsx';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
	className?: string;
	children: React.ReactNode;
	variant?: 'default' | 'outlined' | 'elevated' | 'filled';
	size?: 'small' | 'medium' | 'large';
	interactive?: boolean;
	disabled?: boolean;
}

interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
	className?: string;
	children: React.ReactNode;
}

interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
	className?: string;
	children: React.ReactNode;
}

interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
	className?: string;
	children: React.ReactNode;
}

interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
	className?: string;
	children: React.ReactNode;
	as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

interface CardDescriptionProps
	extends React.HTMLAttributes<HTMLParagraphElement> {
	className?: string;
	children: React.ReactNode;
}

const cardVariants = {
	default: ['bg-bg-surface', 'border border-border-primary', 'shadow-sm'],
	outlined: ['bg-bg-surface', 'border border-border-secondary', 'shadow-none'],
	elevated: ['bg-bg-surface', 'border-0', 'shadow-lg'],
	filled: ['bg-bg-secondary', 'border-0', 'shadow-none'],
};

const cardSizes = {
	small: 'p-4',
	medium: 'p-6',
	large: 'p-8',
};

const Card = forwardRef<HTMLDivElement, CardProps>(
	(
		{
			className,
			children,
			variant = 'default',
			size = 'medium',
			interactive = false,
			disabled = false,
			...props
		},
		ref,
	) => {
		return (
			<div
				ref={ref}
				className={clsx(
					// Base styles
					'rounded-lg transition-all duration-200 ease-in-out',
					// Variant styles
					cardVariants[variant],
					// Size styles
					cardSizes[size],
					// Interactive styles
					interactive &&
						!disabled && [
							'cursor-pointer',
							'hover:shadow-md',
							'hover:scale-[1.02]',
							'active:scale-[0.98]',
							'focus:outline-none',
							'focus:ring-2',
							'focus:ring-accent-primary',
							'focus:ring-offset-2',
							'focus:ring-offset-bg-base',
						],
					// Disabled styles
					disabled && [
						'opacity-50',
						'cursor-not-allowed',
						'pointer-events-none',
					],
					className,
				)}
				{...props}>
				{children}
			</div>
		);
	},
);

const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
	({ className, children, ...props }, ref) => {
		return (
			<div
				ref={ref}
				className={clsx(
					'flex flex-col space-y-1.5',
					'pb-2',
					'border-b border-border-secondary',
					className,
				)}
				{...props}>
				{children}
			</div>
		);
	},
);

const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
	({ className, children, ...props }, ref) => {
		return (
			<div
				ref={ref}
				className={clsx('py-4', className)}
				{...props}>
				{children}
			</div>
		);
	},
);

const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
	({ className, children, ...props }, ref) => {
		return (
			<div
				ref={ref}
				className={clsx(
					'flex items-center justify-between',
					'pt-4',
					'border-t border-border-secondary',
					className,
				)}
				{...props}>
				{children}
			</div>
		);
	},
);

const CardTitle = forwardRef<HTMLHeadingElement, CardTitleProps>(
	({ className, children, as: Component = 'h3', ...props }, ref) => {
		return (
			<Component
				ref={ref}
				className={clsx(
					'text-lg font-semibold leading-none tracking-tight',
					'text-text-primary',
					className,
				)}
				{...props}>
				{children}
			</Component>
		);
	},
);

const CardDescription = forwardRef<HTMLParagraphElement, CardDescriptionProps>(
	({ className, children, ...props }, ref) => {
		return (
			<p
				ref={ref}
				className={clsx(
					'text-sm text-text-secondary',
					'leading-relaxed',
					className,
				)}
				{...props}>
				{children}
			</p>
		);
	},
);

Card.displayName = 'Card';
CardHeader.displayName = 'CardHeader';
CardContent.displayName = 'CardContent';
CardFooter.displayName = 'CardFooter';
CardTitle.displayName = 'CardTitle';
CardDescription.displayName = 'CardDescription';

export default Card;
export { CardHeader, CardContent, CardFooter, CardTitle, CardDescription };
export type {
	CardProps,
	CardHeaderProps,
	CardContentProps,
	CardFooterProps,
	CardTitleProps,
	CardDescriptionProps,
};
