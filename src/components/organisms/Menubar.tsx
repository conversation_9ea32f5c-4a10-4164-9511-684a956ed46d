import Link from 'next/link';
import { useState } from 'react';
import { Button, Typography } from '@/components/atoms';
import { useAuthStore } from '@/providers/auth-store-provider';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

interface MenubarProps {
	logo: React.ReactNode;
	links: {
		title: string;
		href: string;
		startIcon?: React.ReactNode;
		endIcon?: React.ReactNode;
		isActive?: boolean;
		onClick?: () => void;
		className?: string;
		disabled?: boolean;
	}[];
}

const Menubar: React.FC<MenubarProps> = ({ logo, links }) => {
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const user = useAuthStore((state) => state.user);
	const logout = useAuthStore((state) => state.logout);
	const isLoading = useAuthStore((state) => state.isLoading);

	const toggleMobileMenu = () => {
		setIsMobileMenuOpen(!isMobileMenuOpen);
	};

	const closeMobileMenu = () => {
		setIsMobileMenuOpen(false);
	};

	return (
		<header className='bg-bg-surface border-b border-border-secondary shadow-sm relative'>
			{/* Desktop Header */}
			<div className='flex items-center justify-between h-16 px-2'>
				{/* Logo */}
				<div className='flex-shrink-0'>
					<Typography
						variant='h5'
						weight='semibold'>
						{logo}
					</Typography>
				</div>

				{/* Desktop Navigation */}
				<nav className='hidden md:flex items-center gap-1.5'>
					{links.map((link, index) => (
						<Link
							key={index}
							href={link.href}
							className={`flex items-center gap-1 px-1.5 py-0.5 rounded-md transition-colors duration-200 ${
								link.isActive
									? 'bg-accent-primary/10 text-accent-primary'
									: 'text-text-secondary hover:text-text-primary hover:bg-bg-surface'
							} ${link.className}`}
							onClick={link.onClick}
							aria-disabled={link.disabled}>
							{link.startIcon && (
								<span className='flex-shrink-0 w-4 h-4'>{link.startIcon}</span>
							)}
							<Typography
								variant='body'
								weight='medium'>
								{link.title}
							</Typography>
							{link.endIcon && (
								<span className='flex-shrink-0 w-4 h-4'>{link.endIcon}</span>
							)}
						</Link>
					))}
				</nav>

				{/* Desktop User Menu */}
				{user && (
					<div className='hidden md:flex items-center gap-2'>
						<Typography
							variant='body'
							color='secondary'
							className='hidden lg:block'>
							{user.fullName}
						</Typography>
						<Button
							variant='ghost'
							size='sm'
							onClick={logout}
							disabled={isLoading}>
							{isLoading ? 'Logging out...' : 'Logout'}
						</Button>
					</div>
				)}

				{/* Mobile Menu Button */}
				<div className='md:hidden flex items-center gap-1.5'>
					{user && (
						<Button
							variant='ghost'
							size='sm'
							onClick={logout}
							disabled={isLoading}
							className='text-xs'>
							{isLoading ? 'Logging out...' : 'Logout'}
						</Button>
					)}
					<button
						onClick={toggleMobileMenu}
						className='p-1 rounded-md hover:bg-bg-surface focus:outline-none focus:ring-2 focus:ring-accent-primary'
						aria-label='Toggle mobile menu'
						aria-expanded={isMobileMenuOpen}>
						{isMobileMenuOpen ? (
							<XMarkIcon className='w-6 h-6' />
						) : (
							<Bars3Icon className='w-6 h-6' />
						)}
					</button>
				</div>
			</div>

			{/* Mobile Navigation Menu */}
			{isMobileMenuOpen && (
				<div className='md:hidden absolute top-full left-0 right-0 bg-bg-surface border-b border-border-primary shadow-lg z-50'>
					<nav className='px-2 py-2 space-y-1'>
						{links.map((link, index) => (
							<Link
								key={index}
								href={link.href}
								className={`flex items-center gap-1.5 px-1.5 py-1 rounded-md transition-colors duration-200 w-full ${
									link.isActive
										? 'bg-accent-primary/10 text-accent-primary'
										: 'text-text-secondary hover:text-text-primary hover:bg-bg-surface'
								} ${link.className}`}
								onClick={() => {
									link.onClick?.();
									closeMobileMenu();
								}}
								aria-disabled={link.disabled}>
								{link.startIcon && (
									<span className='flex-shrink-0 w-4 h-4'>
										{link.startIcon}
									</span>
								)}
								<Typography
									variant='body'
									weight='medium'
									className='flex-1'>
									{link.title}
								</Typography>
								{link.endIcon && (
									<span className='flex-shrink-0 w-4 h-4'>{link.endIcon}</span>
								)}
							</Link>
						))}

						{/* Mobile User Info */}
						{user && (
							<div className='pt-2 mt-2 border-t border-border-primary'>
								<div className='px-1.5 py-1'>
									<Typography
										variant='body-sm'
										color='secondary'>
										Signed in as
									</Typography>
									<Typography
										variant='body'
										weight='medium'
										className='mt-50'>
										{user.fullName}
									</Typography>
								</div>
							</div>
						)}
					</nav>
				</div>
			)}

			{/* Mobile Menu Backdrop */}
			{isMobileMenuOpen && (
				<div
					className='md:hidden fixed inset-0 bg-black/20 z-40'
					onClick={closeMobileMenu}
				/>
			)}
		</header>
	);
};

Menubar.displayName = 'Menubar';
export default Menubar;
