import Link from 'next/link';
import { useState } from 'react';
import { Button, Typography } from '@/components/atoms';
import { useAuth } from '@/providers/AuthProvider';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

interface MenubarProps {
	logo: React.ReactNode;
	links: {
		title: string;
		href: string;
		startIcon?: React.ReactNode;
		endIcon?: React.ReactNode;
		isActive?: boolean;
		onClick?: () => void;
		className?: string;
		disabled?: boolean;
	}[];
}

const Menubar: React.FC<MenubarProps> = ({ logo, links }) => {
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const { user, logout, isLoading } = useAuth();

	const toggleMobileMenu = () => {
		setIsMobileMenuOpen(!isMobileMenuOpen);
	};

	const closeMobileMenu = () => {
		setIsMobileMenuOpen(false);
	};

	return (
		<header className='bg-surface-base border-b border-border-base shadow-small relative'>
			{/* Desktop Header */}
			<div className='flex items-center justify-between h-16 px-200'>
				{/* Logo */}
				<div className='flex-shrink-0'>
					<Typography
						variant='h5'
						weight='semibold'>
						{logo}
					</Typography>
				</div>

				{/* Desktop Navigation */}
				<nav className='hidden md:flex items-center gap-150'>
					{links.map((link, index) => (
						<Link
							key={index}
							href={link.href}
							className={`flex items-center gap-100 px-150 py-75 rounded-button transition-colors duration-medium ${
								link.isActive
									? 'bg-accent-background-default/10 text-accent-content-default'
									: 'text-text-secondary hover:text-text-primary hover:bg-background-layer-1'
							} ${link.className}`}
							onClick={link.onClick}
							aria-disabled={link.disabled}>
							{link.startIcon && (
								<span className='flex-shrink-0 w-4 h-4'>{link.startIcon}</span>
							)}
							<Typography
								variant='body'
								weight='medium'>
								{link.title}
							</Typography>
							{link.endIcon && (
								<span className='flex-shrink-0 w-4 h-4'>{link.endIcon}</span>
							)}
						</Link>
					))}
				</nav>

				{/* Desktop User Menu */}
				{user && (
					<div className='hidden md:flex items-center gap-200'>
						<Typography
							variant='body'
							color='secondary'
							className='hidden lg:block'>
							{user.fullName}
						</Typography>
						<Button
							variant='ghost'
							size='sm'
							onClick={logout}
							disabled={isLoading}>
							{isLoading ? 'Logging out...' : 'Logout'}
						</Button>
					</div>
				)}

				{/* Mobile Menu Button */}
				<div className='md:hidden flex items-center gap-150'>
					{user && (
						<Button
							variant='ghost'
							size='sm'
							onClick={logout}
							disabled={isLoading}
							className='text-xs'>
							{isLoading ? 'Logging out...' : 'Logout'}
						</Button>
					)}
					<button
						onClick={toggleMobileMenu}
						className='p-100 rounded-button hover:bg-background-layer-1 focus:outline-none focus:ring-2 focus:ring-accent-border-focus'
						aria-label='Toggle mobile menu'
						aria-expanded={isMobileMenuOpen}>
						{isMobileMenuOpen ? (
							<XMarkIcon className='w-6 h-6' />
						) : (
							<Bars3Icon className='w-6 h-6' />
						)}
					</button>
				</div>
			</div>

			{/* Mobile Navigation Menu */}
			{isMobileMenuOpen && (
				<div className='md:hidden absolute top-full left-0 right-0 bg-surface-base border-b border-border-base shadow-large z-50'>
					<nav className='px-200 py-200 space-y-100'>
						{links.map((link, index) => (
							<Link
								key={index}
								href={link.href}
								className={`flex items-center gap-150 px-150 py-100 rounded-button transition-colors duration-medium w-full ${
									link.isActive
										? 'bg-accent-background-default/10 text-accent-content-default'
										: 'text-text-secondary hover:text-text-primary hover:bg-background-layer-1'
								} ${link.className}`}
								onClick={() => {
									link.onClick?.();
									closeMobileMenu();
								}}
								aria-disabled={link.disabled}>
								{link.startIcon && (
									<span className='flex-shrink-0 w-4 h-4'>
										{link.startIcon}
									</span>
								)}
								<Typography
									variant='body'
									weight='medium'
									className='flex-1'>
									{link.title}
								</Typography>
								{link.endIcon && (
									<span className='flex-shrink-0 w-4 h-4'>{link.endIcon}</span>
								)}
							</Link>
						))}

						{/* Mobile User Info */}
						{user && (
							<div className='pt-200 mt-200 border-t border-border-base'>
								<div className='px-150 py-100'>
									<Typography
										variant='body-sm'
										color='secondary'>
										Signed in as
									</Typography>
									<Typography
										variant='body'
										weight='medium'
										className='mt-50'>
										{user.fullName}
									</Typography>
								</div>
							</div>
						)}
					</nav>
				</div>
			)}

			{/* Mobile Menu Backdrop */}
			{isMobileMenuOpen && (
				<div
					className='md:hidden fixed inset-0 bg-black/20 z-40'
					onClick={closeMobileMenu}
				/>
			)}
		</header>
	);
};

Menubar.displayName = 'Menubar';
export default Menubar;
