import { useState, useEffect } from 'react';
import { Field, Label, Fieldset } from '@headlessui/react';
import {
	Input,
	Textarea,
	Button,
	Alert,
	<PERSON><PERSON><PERSON><PERSON>,
	Typography,
} from '@/components/atoms';
import type { Project } from '@/components/templates/project/ProjectsGrid';
import { Dialog } from '@/components/molecules';

interface EditProjectDialogProps {
	isOpen: boolean;
	onClose: () => void;
	project: Project | null;
	onProjectUpdated?: (
		id: string,
		projectData: {
			name: string;
			description: string;
		},
	) => Promise<Project | null>;
}

const EditProjectDialog: React.FC<EditProjectDialogProps> = ({
	isOpen,
	onClose,
	project,
	onProjectUpdated,
}) => {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Reset error when dialog opens/closes or project changes
	useEffect(() => {
		if (isOpen && project) {
			setError(null);
		}
	}, [isOpen, project]);

	const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();

		if (!project) {
			setError('No project selected for editing');
			return;
		}

		setError(null);
		setIsSubmitting(true);

		const formData = new FormData(event.currentTarget);
		const name = formData.get('name') as string;
		const description = formData.get('description') as string;

		if (!name.trim()) {
			setError('Project name is required');
			setIsSubmitting(false);
			return;
		}

		if (!description.trim()) {
			setError('Project description is required');
			setIsSubmitting(false);
			return;
		}

		try {
			if (onProjectUpdated) {
				const result = await onProjectUpdated(project.id, {
					name: name.trim(),
					description: description.trim(),
				});

				if (result) {
					// Dialog will be closed by parent component
				} else {
					setError('Failed to update project');
				}
			}
		} catch (error) {
			console.error('Error updating project:', error);
			if (error instanceof Error) {
				setError(`Failed to update project: ${error.message}`);
			} else {
				setError(
					'Failed to update project: Network error or server unavailable',
				);
			}
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		if (!isSubmitting) {
			setError(null);
			onClose();
		}
	};

	return (
		<Dialog
			isOpen={isOpen}
			onClose={handleClose}
			title='Edit Project'
			description='Update your project details.'
			size='md'>
			{error && (
				<Alert
					variant='negative'
					className='mb-3'>
					{error}
				</Alert>
			)}

			<form
				onSubmit={handleSubmit}
				className='space-y-3'>
				<Fieldset className='space-y-3'>
					<Field>
						<Label htmlFor='name'>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'>
								Project Name
							</Typography>
						</Label>
						<Input
							id='name'
							name='name'
							type='text'
							required
							placeholder='Enter project name'
							defaultValue={project?.name || ''}
							className='mt-1'
							disabled={isSubmitting}
						/>
					</Field>

					<Field>
						<Label htmlFor='description'>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'>
								Description
							</Typography>
						</Label>
						<Textarea
							id='description'
							name='description'
							rows={4}
							required
							placeholder='Describe your project...'
							defaultValue={project?.description || ''}
							disabled={isSubmitting}
							className='mt-1'
						/>
					</Field>
				</Fieldset>

				<div className='flex flex-col sm:flex-row justify-end gap-1.5 pt-2 border-t border-bg-primary'>
					<Button
						type='button'
						variant='secondary'
						onClick={handleClose}
						disabled={isSubmitting}
						className='order-2 sm:order-1'>
						Cancel
					</Button>
					<Button
						type='submit'
						variant='primary'
						disabled={isSubmitting}
						className='order-1 sm:order-2'>
						{isSubmitting && <ButtonLoader />}
						{isSubmitting ? 'Updating...' : 'Update Project'}
					</Button>
				</div>
			</form>
		</Dialog>
	);
};

export default EditProjectDialog;
