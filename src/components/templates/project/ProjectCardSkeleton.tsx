import { memo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter } from '@/components/atoms';

const ProjectCardSkeleton = memo(() => (
	<Card
		variant='outlined'
		size='medium'
		className='h-full animate-pulse'>
		<CardHeader>
			{/* Title skeleton */}
			<div className='h-6 bg-background-layer-2 rounded mb-2'></div>
			{/* Description skeleton */}
			<div className='h-4 bg-background-layer-2 rounded w-3/4'></div>
		</CardHeader>
		<CardContent>
			{/* Created date skeleton */}
			<div className='h-4 bg-background-layer-2 rounded mb-2 w-1/2'></div>
			{/* Updated date skeleton */}
			<div className='h-4 bg-background-layer-2 rounded w-1/2'></div>
		</CardContent>
		<CardFooter>
			{/* Button skeleton */}
			<div className='h-8 bg-background-layer-2 rounded w-16'></div>
		</CardFooter>
	</Card>
));

ProjectCardSkeleton.displayName = 'ProjectCardSkeleton';

export default ProjectCardSkeleton;
