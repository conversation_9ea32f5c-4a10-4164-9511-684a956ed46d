import { useState } from 'react';
import { Field, Label, Fieldset } from '@headlessui/react';

import {
	Input,
	Textarea,
	Button,
	Alert,
	ButtonL<PERSON><PERSON>,
	Typography,
} from '@/components/atoms';
import { ProjectAPI } from '@/api/ProjectApi';
import {
	isApiSuccess,
	extractApiData,
	getApiErrorMessage,
} from '@/common/utils/apiResponse';
import { Dialog } from '@/components/molecules';

interface CreateProjectDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onProjectCreated?: (projectData: {
		name: string;
		description: string;
	}) => Promise<{ id: string; name: string; description: string } | null>;
}

const CreateProjectDialog: React.FC<CreateProjectDialogProps> = ({
	isOpen,
	onClose,
	onProjectCreated,
}) => {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setError(null);
		setIsSubmitting(true);

		const formData = new FormData(event.currentTarget);
		const name = formData.get('name') as string;
		const description = formData.get('description') as string;

		if (!name.trim()) {
			setError('Project name is required');
			setIsSubmitting(false);
			return;
		}

		if (!description.trim()) {
			setError('Project description is required');
			setIsSubmitting(false);
			return;
		}

		try {
			if (onProjectCreated) {
				const result = await onProjectCreated({
					name: name.trim(),
					description: description.trim(),
				});

				if (result) {
					// Reset form on success
					(event.target as HTMLFormElement).reset();
					// Dialog will be closed by parent component
				} else {
					setError('Failed to create project');
				}
			} else {
				// Fallback to direct API call if no callback provided
				const api = new ProjectAPI();
				const response = await api.createProject({
					name: name.trim(),
					description: description.trim(),
				});

				if (isApiSuccess(response)) {
					const projectData = extractApiData<{
						id: string;
						name: string;
						description: string;
					}>(response);

					if (projectData) {
						onClose();
						// Reset form
						(event.target as HTMLFormElement).reset();
					} else {
						setError('Failed to create project: Invalid response data');
					}
				} else {
					const errorMessage = getApiErrorMessage(response);
					setError(errorMessage);
				}
			}
		} catch (error) {
			console.error('Error creating project:', error);
			if (error instanceof Error) {
				setError(`Failed to create project: ${error.message}`);
			} else {
				setError(
					'Failed to create project: Network error or server unavailable',
				);
			}
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		if (!isSubmitting) {
			setError(null);
			onClose();
		}
	};

	return (
		<Dialog
			isOpen={isOpen}
			onClose={handleClose}
			title='Create New Project'
			description='Create a new project to organize your creative work.'
			size='md'>
			{error && (
				<Alert
					variant='negative'
					className='mb-300'>
					{error}
				</Alert>
			)}

			<form
				onSubmit={handleSubmit}
				className='space-y-300'>
				<Fieldset className='space-y-300'>
					<Field>
						<Label htmlFor='name'>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'>
								Project Name
							</Typography>
						</Label>
						<Input
							id='name'
							name='name'
							type='text'
							required
							placeholder='Enter project name'
							className='mt-100'
							disabled={isSubmitting}
						/>
					</Field>

					<Field>
						<Label htmlFor='description'>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'>
								Description
							</Typography>
						</Label>
						<Textarea
							id='description'
							name='description'
							rows={4}
							required
							placeholder='Describe your project...'
							disabled={isSubmitting}
							className='mt-100'
						/>
					</Field>
				</Fieldset>

				<div className='flex flex-col sm:flex-row justify-end gap-150 pt-200 border-t border-border-base'>
					<Button
						type='button'
						variant='secondary'
						onClick={handleClose}
						disabled={isSubmitting}
						className='order-2 sm:order-1'>
						Cancel
					</Button>
					<Button
						type='submit'
						variant='primary'
						disabled={isSubmitting}
						className='order-1 sm:order-2'>
						{isSubmitting && <ButtonLoader />}
						{isSubmitting ? 'Creating...' : 'Create Project'}
					</Button>
				</div>
			</form>
		</Dialog>
	);
};

export default CreateProjectDialog;
