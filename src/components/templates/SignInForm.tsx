import { Field, Label, Fieldset } from '@headlessui/react';
import {
	Input,
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Typography,
} from '@/components/atoms';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/providers/AuthProvider';

export default function SignInForm() {
	const [message, setMessage] = useState<string | null>(null);
	const router = useRouter();
	const { login, isLoading, error, clearError } = useAuth();

	useEffect(() => {
		const urlMessage = router.query.message as string;
		if (urlMessage) {
			setMessage(urlMessage);
		}
	}, [router.query.message]);

	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		clearError();

		const formData = new FormData(event.currentTarget);
		const email = formData.get('email') as string;
		const password = formData.get('password') as string;

		if (!email || !password) {
			return;
		}

		try {
			const result = await login(email, password);

			if (result.success) {
				// Redirect to intended page or home
				const redirect = router.query.redirect as string;
				router.push(redirect || '/');
			}
		} catch (error) {
			console.error('Login error:', error);
		}
	};
	return (
		<div className='w-full'>
			<div className='text-center mb-400'>
				<Typography
					variant='h2'
					responsive
					className='mb-100'>
					Sign in to your account
				</Typography>
				<Typography
					variant='body'
					color='secondary'>
					Welcome back! Please enter your details.
				</Typography>
			</div>

			{message && (
				<Alert
					className='mb-300'
					variant='default'>
					{message}
				</Alert>
			)}
			{error && (
				<Alert
					className='mb-300'
					variant='negative'>
					{error}
				</Alert>
			)}

			<form
				onSubmit={onSubmit}
				className='space-y-300 sm:space-y-400'>
				<Fieldset className='space-y-300'>
					<Field>
						<Label>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'
								className='mb-100'>
								Email address
							</Typography>
						</Label>
						<Input
							id='email'
							name='email'
							type='email'
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='w-full'
							inputMode='email'
						/>
					</Field>

					<Field>
						<Label>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'
								className='mb-100'>
								Password
							</Typography>
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='current-password'
							placeholder='●●●●●●●●'
							className='w-full'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					size='lg'
					disabled={isLoading}
					type='submit'
					className='w-full mt-400'>
					{isLoading && <ButtonLoader />}
					{isLoading ? 'Signing in...' : 'Sign in'}
				</Button>
			</form>
		</div>
	);
}
