import { useRouter } from 'next/router';
import { useAuth } from '@/providers/AuthProvider';
import { useEffect, useState } from 'react';
import { FullScreenLoader, Typography } from '@/components/atoms';

export default function AuthTemplate({
	children,
}: {
	children: React.ReactNode;
}) {
	const router = useRouter();
	const { user, isLoading } = useAuth();
	const [isRedirecting, setIsRedirecting] = useState(false);

	useEffect(() => {
		// Only redirect if user exists and we're not already redirecting
		if (user && !isRedirecting) {
			setIsRedirecting(true);
			router.replace('/').catch(console.error);
		}
	}, [router, user, isRedirecting]);

	// Show loading while checking authentication
	if (isLoading) {
		return <FullScreenLoader text='Loading...' />;
	}

	// If user is authenticated and redirecting, show loading
	if (isRedirecting) {
		return (
			<div className='flex min-h-screen flex-1 flex-col justify-center items-center bg-background-base'>
				<div className='text-center'>
					<Typography
						variant='body'
						color='secondary'>
						Redirecting...
					</Typography>
				</div>
			</div>
		);
	}

	// Otherwise, render children (the auth form)
	return (
		<div className='min-h-screen bg-background-base flex flex-col justify-center py-400 sm:py-600 lg:py-800'>
			<div className='flex-1 flex flex-col justify-center'>{children}</div>
		</div>
	);
}
