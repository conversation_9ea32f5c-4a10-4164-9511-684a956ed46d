import { useRouter } from 'next/router';
import { useAuthStore } from '@/providers/auth-store-provider';
import { useEffect, useState } from 'react';
import { FullScreenLoader, Typography } from '@/components/atoms';

export default function AuthTemplate({
	children,
}: {
	children: React.ReactNode;
}) {
	const router = useRouter();
	const user = useAuthStore((state) => state.user);
	const isHydrated = useAuthStore((state) => state.isHydrated);
	const [isRedirecting, setIsRedirecting] = useState(false);

	useEffect(() => {
		// Only redirect if hydrated and user exists
		if (isHydrated && user && !isRedirecting) {
			setIsRedirecting(true);
			router.replace('/').catch(console.error);
		}
	}, [router, user, isHydrated, isRedirecting]);

	// Show loading while hydrating
	if (!isHydrated) {
		return <FullScreenLoader text='Loading...' />;
	}

	// If user is authenticated and redirecting, show loading
	if (isRedirecting) {
		return (
			<div className='flex min-h-screen flex-1 flex-col justify-center items-center bg-bg-base'>
				<div className='text-center'>
					<Typography
						variant='body'
						color='secondary'>
						Redirecting...
					</Typography>
				</div>
			</div>
		);
	}

	// Otherwise, render children (the auth form)
	return (
		<div className='min-h-screen bg-bg-base flex flex-col justify-center py-8 sm:py-12 lg:py-16'>
			<div className='flex-1 flex flex-col justify-center'>{children}</div>
		</div>
	);
}
