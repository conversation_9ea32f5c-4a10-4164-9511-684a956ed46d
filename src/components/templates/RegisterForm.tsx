import { Field, Label, Fieldset } from '@headlessui/react';
import {
	Input,
	<PERSON><PERSON>,
	<PERSON>ert,
	<PERSON><PERSON><PERSON><PERSON>,
	InlineLoader,
	Typography,
} from '@/components/atoms';
import { useAuth } from '@/hooks/useAuth';

export default function RegisterForm({
	invitation,
	invitationError,
	isLoadingInvitation = false,
}: {
	invitation?: {
		token?: string;
		email?: string;
		status?: string;
	};
	invitationError?: string;
	isLoadingInvitation?: boolean;
}) {
	const { isLoading, error, clearError, register } = useAuth();

	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		clearError();

		const formData = new FormData(event.currentTarget);
		const fullName = formData.get('fullName') as string;
		const email =
			(formData.get('email') as string) ||
			(formData.get('invitationEmail') as string) ||
			'';
		const password = formData.get('password') as string;

		if (!fullName || !email || !password) {
			return;
		}

		const credentials = {
			fullName,
			email,
			password,
			...(invitation?.token && { invitationToken: invitation.token }),
		};

		try {
			await register(credentials);
		} catch (error) {
			//set error in alert
			console.error('Register error:', error);
		}
	};
	return (
		<div className='w-full'>
			<div className='text-center mb-400'>
				<Typography
					variant='h2'
					responsive
					className='mb-100'>
					Create an account
				</Typography>
				<Typography
					variant='body'
					color='secondary'>
					Get started with your free account today.
				</Typography>
			</div>

			{isLoadingInvitation && (
				<Alert
					className='mb-300'
					variant='default'>
					<InlineLoader text='Validating invitation...' />
				</Alert>
			)}
			{!isLoadingInvitation && invitation?.status === 'PENDING' && (
				<Alert
					className='mb-300'
					variant='default'>
					You have been invited. Please fill in your details to create an
					account.
				</Alert>
			)}
			{!isLoadingInvitation && invitationError && (
				<Alert
					className='mb-300'
					variant='negative'>
					{invitationError}
				</Alert>
			)}
			{error && (
				<Alert
					className='mb-300'
					variant='negative'>
					{error}
				</Alert>
			)}

			<form
				onSubmit={onSubmit}
				className='space-y-300 sm:space-y-400'>
				{invitation?.email && (
					<input
						type='hidden'
						name='invitationEmail'
						value={invitation.email}
					/>
				)}
				<Fieldset className='space-y-300'>
					<Field>
						<Label htmlFor='fullName'>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'
								className='mb-100'>
								Full Name
							</Typography>
						</Label>
						<Input
							id='fullName'
							name='fullName'
							type='text'
							required
							autoComplete='name'
							placeholder='John Doe'
							className='w-full'
						/>
					</Field>

					<Field>
						<Label htmlFor='email'>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'
								className='mb-100'>
								Email address
							</Typography>
						</Label>
						<Input
							id='email'
							name='email'
							type='email'
							defaultValue={invitation?.email || ''}
							disabled={!!invitation?.email}
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='w-full'
							inputMode='email'
						/>
					</Field>

					<Field>
						<Label htmlFor='password'>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'
								className='mb-100'>
								Password
							</Typography>
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='new-password'
							placeholder='●●●●●●●●'
							className='w-full'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					size='lg'
					disabled={isLoading}
					type='submit'
					className='w-full mt-400'>
					{isLoading && <ButtonLoader />}
					{isLoading ? 'Creating account...' : 'Register'}
				</Button>
			</form>
		</div>
	);
}
