'use client';

import React, {
	createContext,
	useContext,
	useState,
	useEffect,
	useCallback,
	ReactNode,
} from 'react';
import { useRouter } from 'next/router';
import { AuthAPI } from '@/api/AuthApi';
import {
	isApiSuccess,
	extractApiData,
	getApiErrorMessage,
} from '@/common/utils/apiResponse';

export interface User {
	id: string;
	fullName: string;
	email: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
}

interface AuthContextType {
	user: User | null;
	isLoading: boolean;
	error: string | null;
	isAuthenticated: boolean;
	login: (email: string, password: string) => Promise<{ success: boolean }>;
	register: (credentials: RegisterCredentials) => Promise<{ success: boolean }>;
	logout: () => Promise<void>;
	refreshAuth: () => Promise<boolean>;
	clearError: () => void;
}

interface RegisterCredentials {
	fullName: string;
	email: string;
	password: string;
	invitationToken?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
	children: ReactNode;
	initialUser?: User | null;
}

export function AuthProvider({
	children,
	initialUser = null,
}: AuthProviderProps) {
	const [user, setUser] = useState<User | null>(initialUser);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const router = useRouter();

	const isAuthenticated = !!user;

	const clearError = () => setError(null);

	const login = async (
		email: string,
		password: string,
	): Promise<{ success: boolean }> => {
		setIsLoading(true);
		setError(null);

		try {
			const authApi = new AuthAPI();
			const response = await authApi.login({ email, password });

			if (isApiSuccess(response)) {
				// Extract user data from response (no tokens in new flow)
				const userData = extractApiData<User>(response);

				if (userData) {
					setUser(userData);
					return { success: true };
				} else {
					setError('Login successful but user data is missing');
					return { success: false };
				}
			} else {
				const errorMessage = getApiErrorMessage(response);
				setError(errorMessage);
				return { success: false };
			}
		} catch (error) {
			console.error('Login failed:', error);
			setError('An unexpected error occurred. Please try again.');
			return { success: false };
		} finally {
			setIsLoading(false);
		}
	};

	const register = async (
		credentials: RegisterCredentials,
	): Promise<{ success: boolean }> => {
		setIsLoading(true);
		setError(null);

		try {
			const authApi = new AuthAPI();
			const response = await authApi.register(credentials);

			if (isApiSuccess(response)) {
				// Extract user data from response (no tokens in new flow)
				const userData = extractApiData<User>(response);

				if (userData) {
					setUser(userData);
					return { success: true };
				} else {
					setError('Registration successful but user data is missing');
					return { success: false };
				}
			} else {
				const errorMessage = getApiErrorMessage(response);
				setError(errorMessage);
				return { success: false };
			}
		} catch (error) {
			console.error('Registration failed:', error);
			setError('An unexpected error occurred. Please try again.');
			return { success: false };
		} finally {
			setIsLoading(false);
		}
	};

	const logout = async (): Promise<void> => {
		setIsLoading(true);

		try {
			if (user?.id) {
				const authApi = new AuthAPI();
				await authApi.logout(user.id);
			}
		} catch (error) {
			console.error('Logout API call failed:', error);
			// Continue with logout even if API call fails
		}

		// Clear user state
		setUser(null);
		setError(null);
		setIsLoading(false);

		// Redirect to signin
		router.push('/signin');
	};

	const refreshAuth = useCallback(async (): Promise<boolean> => {
		try {
			const authApi = new AuthAPI();
			const response = await authApi.refresh();

			if (isApiSuccess(response)) {
				const userData = extractApiData<User>(response);

				if (userData) {
					setUser(userData);
					return true;
				}
			}

			// Refresh failed, clear user state
			setUser(null);
			return false;
		} catch (error) {
			console.error('Auth refresh failed:', error);
			setUser(null);
			return false;
		}
	}, []);

	// Check authentication status on mount and route changes
	useEffect(() => {
		const checkAuth = async () => {
			// Only check if we don't have a user and we're not already loading
			if (!user && !isLoading) {
				setIsLoading(true);
				const isValid = await refreshAuth();
				setIsLoading(false);

				// If auth check fails and we're on a protected route, redirect to signin
				if (
					!isValid &&
					router.pathname !== '/signin' &&
					router.pathname !== '/register'
				) {
					// Check if current route requires authentication
					const publicRoutes = ['/signin', '/register', '/404', '/500'];
					if (!publicRoutes.includes(router.pathname)) {
						router.push(
							'/signin?redirect=' + encodeURIComponent(router.pathname),
						);
					}
				}
			}
		};

		checkAuth();
	}, [router.pathname, user, isLoading, refreshAuth, router]); // Include all dependencies

	const value: AuthContextType = {
		user,
		isLoading,
		error,
		isAuthenticated,
		login,
		register,
		logout,
		refreshAuth,
		clearError,
	};

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth(): AuthContextType {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error('useAuth must be used within an AuthProvider');
	}
	return context;
}
