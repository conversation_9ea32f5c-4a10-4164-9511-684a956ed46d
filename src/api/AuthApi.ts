import { CommonFunction } from '@/common/Function/Function';
import { Api } from '@/common/StandardApi';
import { ApiURL } from '@/config/config';

interface LoginRequest {
	email: string;
	password: string;
}

interface RegisterRequest {
	fullName: string;
	email: string;
	password: string;
	invitationToken?: string;
}

class AuthAPI {
	_api: Api;
	appOrigin: string;

	constructor(appOrigin: string = 'BO') {
		this._api = new Api(ApiURL);
		this.appOrigin = appOrigin;
	}

	async login(body: LoginRequest) {
		return await this._api.post(
			`auth/signin`,
			JSON.stringify(body),
			CommonFunction.createHeaders({ withToken: false }),
		);
	}

	async register(body: RegisterRequest) {
		return await this._api.post(
			`auth/register`,
			JSON.stringify(body),
			CommonFunction.createHeaders({ withToken: false }),
		);
	}

	async logout(userId: string) {
		return this._api.post(
			`auth/logout`,
			JSON.stringify({ userId }),
			CommonFunction.createHeaders({ withToken: false }), // No token needed, uses cookies
		);
	}

	async refresh() {
		// New refresh endpoint doesn't need userId - reads refresh token from cookies
		return this._api.post(
			`auth/refresh`,
			null, // No body needed
			CommonFunction.createHeaders({ withToken: false }), // No token needed, uses cookies
		);
	}
}
export { AuthAPI };
