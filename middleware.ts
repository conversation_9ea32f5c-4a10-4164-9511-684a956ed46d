import { NextRequest, NextResponse } from 'next/server';
import * as jose from 'jose';

// Route configuration with specific requirements
const routeConfig = {
	// Public routes (no authentication required)
	public: ['/404', '/500', '/401', '/403'],

	// Auth routes (redirect if already authenticated)
	auth: ['/signin', '/register'],

	// Protected routes with role/status requirements
	protected: {
		'/': { roles: ['ADMIN', 'USER'], statuses: ['ACTIVE'] },
		'/projects': { roles: ['ADMIN', 'USER'], statuses: ['ACTIVE'] },
	},

	// Status-specific routes (for inactive/rejected users)
	statusRoutes: {
		'/account-status': {
			roles: ['ADMIN', 'USER'],
			statuses: ['INACTIVE', 'REJECTED'],
		},
		'/unauthorized': {
			roles: ['ADMIN', 'USER'],
			statuses: ['ACTIVE', 'INACTIVE', 'REJECTED'],
		},
	},
};

// Helper functions
const isPublicRoute = (pathname: string) =>
	routeConfig.public.some((route) => pathname.startsWith(route));

const isAuthRoute = (pathname: string) =>
	routeConfig.auth.some((route) => pathname.startsWith(route));

const getRouteConfig = (pathname: string) => {
	// Check protected routes
	for (const [route, config] of Object.entries(routeConfig.protected)) {
		if (pathname === route || (route !== '/' && pathname.startsWith(route))) {
			return { type: 'protected', config };
		}
	}

	// Check status routes
	for (const [route, config] of Object.entries(routeConfig.statusRoutes)) {
		if (pathname === route || pathname.startsWith(route)) {
			return { type: 'status', config };
		}
	}

	return null;
};

export async function middleware(req: NextRequest) {
	const pathname = req.nextUrl.pathname;
	const url = req.nextUrl.clone();

	// Skip middleware for public routes
	if (isPublicRoute(pathname)) {
		return NextResponse.next();
	}

	const isAuth = isAuthRoute(pathname);
	const routeInfo = getRouteConfig(pathname);

	// Get access token from cookies (new cookie-based auth)
	const token = req.cookies.get('accessToken')?.value;

	// If accessing protected route without token, redirect to signin
	if (routeInfo && !token) {
		url.pathname = '/signin';
		url.searchParams.set('redirect', pathname);
		return NextResponse.redirect(url);
	}

	// If we have a token, verify it
	if (token) {
		try {
			const secret = process.env.NEXT_PUBLIC_JWT_SECRET;
			if (!secret) {
				console.error('JWT_SECRET is not defined');
				if (routeInfo) {
					url.pathname = '/signin';
					return NextResponse.redirect(url);
				}
				return NextResponse.next();
			}

			const { payload } = await jose.jwtVerify(
				token,
				new TextEncoder().encode(secret),
			);

			const userRole = payload.role as 'ADMIN' | 'USER';
			const userStatus = payload.status as 'ACTIVE' | 'INACTIVE' | 'REJECTED';
			const tokenType = payload.type as string;

			console.info(payload);
			// Check if it's an auth token
			if (tokenType !== 'auth') {
				url.pathname = '/signin';
				url.searchParams.set('error', 'invalid_token');
				const response = NextResponse.redirect(url);
				response.cookies.delete('accessToken');
				response.cookies.delete('refreshToken');
				return response;
			}

			// If user is authenticated and trying to access auth routes, redirect to home
			if (isAuth) {
				url.pathname = '/';
				return NextResponse.redirect(url);
			}

			// Check route-specific authorization
			if (routeInfo) {
				const { config } = routeInfo;

				// Check role authorization
				if (!config.roles.includes(userRole)) {
					url.pathname = '/unauthorized';
					return NextResponse.redirect(url);
				}

				// Check status authorization
				if (!config.statuses.includes(userStatus)) {
					// Redirect inactive/rejected users to account status page
					if (userStatus === 'INACTIVE' || userStatus === 'REJECTED') {
						url.pathname = '/account-status';
						return NextResponse.redirect(url);
					}
					// For other status issues, redirect to unauthorized
					url.pathname = '/unauthorized';
					return NextResponse.redirect(url);
				}
			}

			// Add user info to headers for the request
			const response = NextResponse.next();
			response.headers.set('x-user-email', payload.email as string);
			response.headers.set('x-user-role', userRole);
			response.headers.set('x-user-status', userStatus);

			return response;
		} catch {
			// Clear invalid token and redirect if accessing protected route
			if (routeInfo) {
				url.pathname = '/signin';
				url.searchParams.set('error', 'session_expired');
				const response = NextResponse.redirect(url);
				response.cookies.delete('accessToken');
				response.cookies.delete('refreshToken');
				return response;
			}

			// For non-protected routes, just clear the invalid token and continue
			const response = NextResponse.next();
			response.cookies.delete('accessToken');
			response.cookies.delete('refreshToken');
			return response;
		}
	}

	// Allow access to public routes and auth routes when not authenticated
	return NextResponse.next();
}
